/* NoteForm.tsx ---------------------------------------------------- */
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form.tsx'
import { useEffect, useState, useRef } from 'react'
import { useForm, useWatch, FieldErrors } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'

import { Info, Loader2, Plus, Clipboard, FileStack, FileText, Upload, Trash2 } from 'lucide-react'
import { message, Alert } from 'antd'
import { generateNote, parseMarkdown, batchGenerateNotes } from '@/services/note.ts'
import { uploadFile } from '@/services/upload.ts'
import { useTaskStore } from '@/store/taskStore'
import { useModelStore } from '@/store/modelStore'
import {
  Toolt<PERSON>,
  <PERSON>lt<PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  TooltipTrigger,
} from '@/components/ui/tooltip.tsx'
import { Checkbox } from '@/components/ui/checkbox.tsx'
import { ScrollArea } from '@/components/ui/scroll-area.tsx'
import { Button } from '@/components/ui/button.tsx'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select.tsx'
import { Input } from '@/components/ui/input.tsx'
import { Textarea } from '@/components/ui/textarea.tsx'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Label } from '@/components/ui/label'
import { noteStyles, noteFormats, videoPlatforms } from '@/constant/note.ts'

/* -------------------- 校验 Schema -------------------- */
const formSchema = z
  .object({
    video_url: z.string(),
    platform: z.string().nonempty('请选择平台'),
    quality: z.enum(['fast', 'medium', 'slow']),
    screenshot: z.boolean().optional(),
    link: z.boolean().optional(),
    model_name: z.string().nonempty('请选择模型'),
    provider_id: z.string().optional(),
    task_id: z.string().optional(),
    format: z.array(z.string()).default([]),
    style: z.string().nonempty('请选择笔记生成风格'),
    extras: z.string().optional(),
    video_understanding: z.boolean().optional(),
    video_interval: z.coerce.number().min(1).max(30).default(4).optional(),
    grid_size: z
      .tuple([z.coerce.number().min(1).max(10), z.coerce.number().min(1).max(10)])
      .default([3, 3])
      .optional(),
  })
  .superRefine(({ video_url, platform }, ctx) => {
    if (platform === 'local' || platform === 'douyin') {
      if (!video_url) {
        ctx.addIssue({ code: 'custom', message: '本地视频路径不能为空', path: ['video_url'] })
      }
    } else {
      try {
        const url = new URL(video_url)
        if (!['http:', 'https:'].includes(url.protocol)) throw new Error()
      } catch {
        ctx.addIssue({ code: 'custom', message: '请输入正确的视频链接', path: ['video_url'] })
      }
    }
  })

type NoteFormValues = z.infer<typeof formSchema>

/* -------------------- 可复用子组件 -------------------- */
const SectionHeader = ({ title, tip }: { title: string; tip?: string }) => (
  <div className="my-3 flex items-center justify-between">
    <h2 className="block">{title}</h2>
    {tip && (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Info className="hover:text-primary h-4 w-4 cursor-pointer text-neutral-400" />
          </TooltipTrigger>
          <TooltipContent className="text-xs">{tip}</TooltipContent>
        </Tooltip>
      </TooltipProvider>
    )}
  </div>
)

const CheckboxGroup = ({
  value = [],
  onChange,
  disabledMap,
}: {
  value?: string[]
  onChange: (v: string[]) => void
  disabledMap: Record<string, boolean>
}) => (
  <div className="flex flex-wrap space-x-1.5">
    {noteFormats.map(({ label, value: v }) => (
      <label key={v} className="flex items-center space-x-2">
        <Checkbox
          checked={value.includes(v)}
          disabled={disabledMap[v]}
          onCheckedChange={checked =>
            onChange(checked ? [...value, v] : value.filter(x => x !== v))
          }
        />
        <span>{label}</span>
      </label>
    ))}
  </div>
)

/* -------------------- 主组件 -------------------- */
const NoteForm = () => {
  /* ---- 全局状态 ---- */
  const { addPendingTask, currentTaskId, setCurrentTask, getCurrentTask, retryTask } =
    useTaskStore()
  const { loadEnabledModels, modelList, showFeatureHint, setShowFeatureHint } = useModelStore()

  /* ---- 批量处理状态 ---- */
  const [isBatchMode, setIsBatchMode] = useState(false)
  const [markdownContent, setMarkdownContent] = useState('')
  const [extractedLinks, setExtractedLinks] = useState<Array<{
    id: string
    url: string
    title: string
    platform: string
  }>>([])
  const [isBatchProcessing, setIsBatchProcessing] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  /* ---- 表单 ---- */
  const form = useForm<NoteFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      video_url: '',
      platform: 'bilibili',
      quality: 'medium',
      model_name: modelList[0]?.model_name || '',
      style: 'minimal',
      video_interval: 4,
      grid_size: [3, 3],
      format: [],
      extras: '',
      video_understanding: false,
      screenshot: false,
      link: false,
    },
  })
  const currentTask = getCurrentTask()

  /* ---- 派生状态（只 watch 一次，提高性能） ---- */
  const platform = useWatch({ control: form.control, name: 'platform' }) as string
  const videoUnderstandingEnabled = useWatch({ control: form.control, name: 'video_understanding' })
  const editing = currentTask && currentTask.id

  /* ---- 副作用 ---- */
  useEffect(() => {
    loadEnabledModels()

    return
  }, [])
  useEffect(() => {
    if (!currentTask) return
    const { formData } = currentTask

    console.log('currentTask.formData.platform:', formData.platform)

    form.reset({
      platform: formData.platform || 'bilibili',
      video_url: formData.video_url || '',
      model_name: formData.model_name || modelList[0]?.model_name || '',
      style: formData.style || 'minimal',
      quality: formData.quality || 'medium',
      extras: formData.extras || '',
      screenshot: formData.screenshot ?? false,
      link: formData.link ?? false,
      video_understanding: formData.video_understanding ?? false,
      video_interval: formData.video_interval ?? 4,
      grid_size: formData.grid_size ?? [3, 3],
      format: formData.format ?? [],
    })
  }, [
    // 当下面任意一个变了，就重新 reset
    currentTaskId,
    // modelList 用来兜底 model_name
    modelList.length,
    // 还要加上 formData 的各字段，或者直接 currentTask
    currentTask?.formData,
  ])

  /* ---- 帮助函数 ---- */
  const isGenerating = () => !['SUCCESS', 'FAILED', undefined].includes(getCurrentTask()?.status)
  const generating = isGenerating()
  const handleFileUpload = async (file: File, cb: (url: string) => void) => {
    const formData = new FormData()
    formData.append('file', file)
    try {
      const { data } = await uploadFile(formData)
      if (data.code === 0) cb(data.data.url)
    } catch (err) {
      console.error('上传失败:', err)
      message.error('上传失败，请重试')
    }
  }

  const onSubmit = async (values: NoteFormValues) => {
    console.log('onSubmit called with values:', values)
    const payload: NoteFormValues = {
      ...values,
      provider_id: modelList.find(m => m.model_name === values.model_name)!.provider_id,
      task_id: currentTaskId || '',
    }
    if (currentTaskId) {
      retryTask(currentTaskId, payload)
      return
    }

    message.success('已提交任务')
    const { data } = await generateNote(payload)
    addPendingTask(data.task_id, values.platform, payload)
  }
  const onInvalid = (errors: FieldErrors<NoteFormValues>) => {
    console.warn('表单校验失败：', errors)
    console.log('当前表单值：', form.getValues())
    message.error('请完善所有必填项后再提交')
  }
  const handleCreateNew = () => {
    // 🔁 这里清空当前任务状态
    // 比如调用 resetCurrentTask() 或者 navigate 到一个新页面
    setCurrentTask(null)
  }

  // 剪贴板粘贴功能
  const handlePasteFromClipboard = async () => {
    try {
      const text = await navigator.clipboard.readText()
      if (text.trim()) {
        form.setValue('video_url', text.trim())
        message.success('已从剪贴板粘贴链接')
      } else {
        message.warning('剪贴板为空')
      }
    } catch (error) {
      console.error('读取剪贴板失败:', error)
      message.error('读取剪贴板失败，请检查浏览器权限')
    }
  }
  /* ---- 批量处理函数 ---- */
  const detectPlatform = (url: string): string => {
    if (url.includes('bilibili.com')) return 'bilibili'
    if (url.includes('youtube.com') || url.includes('youtu.be')) return 'youtube'
    if (url.includes('douyin.com')) return 'douyin'
    if (url.includes('kuaishou.com')) return 'kuaishou'
    return 'unknown'
  }

  const handleFileUploadBatch = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    if (!file.name.endsWith('.md')) {
      message.error('请上传 .md 格式的文件')
      return
    }

    const reader = new FileReader()
    reader.onload = async (e) => {
      const content = e.target?.result as string
      setMarkdownContent(content)
      await handleParseMarkdown(content)
    }
    reader.readAsText(file)
  }

  const handleParseMarkdown = async (content?: string) => {
    const textContent = content || markdownContent
    if (!textContent.trim()) {
      message.error('请输入或上传Markdown内容')
      return
    }

    try {
      const result = await parseMarkdown(textContent)
      if (result) {
        const links = result.links.map((link: any) => ({
          id: `link-${Date.now()}-${Math.random()}`,
          url: link.url,
          title: link.title,
          platform: link.platform
        }))
        setExtractedLinks(links)
        message.success(`解析完成，发现 ${links.length} 个有效视频链接`)
      }
    } catch (error) {
      console.error('解析失败:', error)
      message.error('解析失败，请稍后重试')
    }
  }

  const handleBatchProcess = async () => {
    if (extractedLinks.length === 0) {
      message.error('没有可处理的链接，请先解析Markdown内容')
      return
    }

    const formValues = form.getValues()
    if (!formValues.model_name) {
      message.error('请先选择AI模型')
      return
    }

    setIsBatchProcessing(true)

    try {
      const result = await batchGenerateNotes({
        links: extractedLinks.map(link => ({
          url: link.url,
          title: link.title,
          platform: link.platform
        })),
        quality: formValues.quality,
        model_name: formValues.model_name,
        provider_id: modelList.find(m => m.model_name === formValues.model_name)!.provider_id,
        format: formValues.format,
        style: formValues.style,
        extras: formValues.extras,
        screenshot: formValues.format.includes('screenshot'),
        link: formValues.format.includes('link'),
        video_understanding: formValues.video_understanding,
        video_interval: formValues.video_interval,
        grid_size: formValues.grid_size
      })

      if (result) {
        // 将任务添加到任务存储中
        result.tasks.forEach((task: any) => {
          addPendingTask(task.task_id, task.platform, {
            video_url: task.url,
            platform: task.platform,
            quality: formValues.quality,
            model_name: formValues.model_name,
            provider_id: modelList.find(m => m.model_name === formValues.model_name)!.provider_id
          })
        })

        message.success(`批量处理已提交，共 ${result.total_count} 个任务正在处理中`)

        // 清空批量处理状态
        setExtractedLinks([])
        setMarkdownContent('')
        setIsBatchMode(false)
      }
    } catch (error) {
      console.error('批量处理失败:', error)
      message.error('批量处理失败，请稍后重试')
    } finally {
      setIsBatchProcessing(false)
    }
  }

  const handleRemoveLink = (linkId: string) => {
    setExtractedLinks(prev => prev.filter(link => link.id !== linkId))
  }

  const FormButton = () => {
    if (isBatchMode) {
      return (
        <div className="flex gap-2">
          <Button
            type="button"
            onClick={handleBatchProcess}
            className="flex-1 bg-primary"
            disabled={isBatchProcessing || extractedLinks.length === 0}
          >
            {isBatchProcessing && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {isBatchProcessing ? '批量处理中...' : `批量生成笔记 (${extractedLinks.length})`}
          </Button>
          <Button
            type="button"
            variant="outline"
            onClick={() => {
              setIsBatchMode(false)
              setExtractedLinks([])
              setMarkdownContent('')
            }}
          >
            取消批量
          </Button>
        </div>
      )
    }

    const label = generating ? '正在生成…' : editing ? '重新生成' : '生成笔记'

    return (
      <div className="flex gap-2">
        <Button
          type="submit"
          className={!editing ? 'w-full' : 'w-2/3' + ' bg-primary'}
          disabled={generating}
        >
          {generating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {label}
        </Button>

        {editing && (
          <Button type="button" variant="outline" className="w-1/3" onClick={handleCreateNew}>
            <Plus className="mr-2 h-4 w-4" />
            新建笔记
          </Button>
        )}
      </div>
    )
  }

  /* -------------------- 渲染 -------------------- */

  return (
    <div className="h-full w-full">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit, onInvalid)} className="space-y-4">
          {/* 模式切换按钮 */}
          <div className="flex gap-2 mb-4">
            <Button
              type="button"
              variant={!isBatchMode ? "default" : "outline"}
              size="sm"
              onClick={() => {
                setIsBatchMode(false)
                setExtractedLinks([])
                setMarkdownContent('')
              }}
              className="flex-1"
            >
              <FileText className="mr-2 h-4 w-4" />
              单个处理
            </Button>
            <Button
              type="button"
              variant={isBatchMode ? "default" : "outline"}
              size="sm"
              onClick={() => setIsBatchMode(true)}
              className="flex-1"
            >
              <FileStack className="mr-2 h-4 w-4" />
              批量处理
            </Button>
          </div>

          {/* 顶部按钮 */}
          <FormButton></FormButton>

          {/* 批量处理模式的UI */}
          {isBatchMode && (
            <div className="space-y-4 p-4 border rounded-lg bg-blue-50">
              <div className="flex items-center gap-2">
                <h3 className="text-lg font-semibold text-blue-800">📁 批量处理模式</h3>
                <span className="text-xs bg-blue-200 text-blue-800 px-2 py-1 rounded">已激活</span>
              </div>
              <p className="text-sm text-blue-600">导入包含多个视频链接的Markdown文档，一键批量生成笔记</p>

              {/* 文件上传区域 */}
              <div className="bg-white p-4 rounded-lg border-2 border-dashed border-blue-200">
                <div className="space-y-3">
                  <div className="text-sm font-medium text-gray-700">📄 上传Markdown文件</div>
                  <Input
                    type="file"
                    accept=".md,.markdown,.txt"
                    ref={fileInputRef}
                    onChange={handleFileUploadBatch}
                    className="cursor-pointer"
                  />
                  <p className="text-xs text-gray-500">支持 .md, .markdown, .txt 格式</p>
                </div>
              </div>

              {/* 分隔线 */}
              <div className="flex items-center gap-4">
                <div className="flex-1 border-t border-gray-300"></div>
                <span className="text-sm text-gray-500 bg-blue-50 px-3 py-1 rounded">或</span>
                <div className="flex-1 border-t border-gray-300"></div>
              </div>

              {/* 手动输入区域 */}
              <div className="bg-white p-4 rounded-lg border">
                <div className="space-y-3">
                  <div className="text-sm font-medium text-gray-700">✏️ 直接输入Markdown内容</div>
                  <Textarea
                    placeholder={`粘贴包含视频链接的Markdown内容，例如：

# 我的视频收藏
1. [AI编程教程](https://www.bilibili.com/video/BV1234567890)
2. [Web开发指南](https://www.youtube.com/watch?v=abc123def456)

或者直接粘贴链接：
https://www.bilibili.com/video/BV1234567890
https://www.youtube.com/watch?v=abc123def456`}
                    value={markdownContent}
                    onChange={(e) => setMarkdownContent(e.target.value)}
                    className="min-h-[150px] resize-none"
                  />
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-gray-500">
                      {markdownContent.length} 字符
                    </span>
                    <Button
                      type="button"
                      onClick={() => handleParseMarkdown()}
                      size="sm"
                      variant="outline"
                      disabled={!markdownContent.trim()}
                    >
                      <FileText className="h-4 w-4 mr-2" />
                      解析链接
                    </Button>
                  </div>
                </div>
              </div>

              {/* 解析出的链接列表 */}
              {extractedLinks.length > 0 && (
                <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium text-green-800">🎯 发现的视频链接</span>
                      <span className="bg-green-200 text-green-800 text-xs px-2 py-1 rounded-full">
                        {extractedLinks.length} 个
                      </span>
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => setExtractedLinks([])}
                      className="text-gray-500 hover:text-red-500"
                    >
                      清空全部
                    </Button>
                  </div>

                  <div className="max-h-60 overflow-y-auto space-y-2">
                    {extractedLinks.map((link, index) => (
                      <div key={link.id} className="flex items-center gap-3 p-3 bg-white border rounded-lg">
                        <div className="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium">
                          {index + 1}
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="font-medium truncate text-sm text-gray-900">{link.title}</p>
                          <p className="text-xs text-gray-500 truncate">{link.url}</p>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className={`text-xs px-2 py-1 rounded-full ${
                            link.platform === 'bilibili' ? 'bg-pink-100 text-pink-700' :
                            link.platform === 'youtube' ? 'bg-red-100 text-red-700' :
                            link.platform === 'douyin' ? 'bg-blue-100 text-blue-700' :
                            'bg-gray-100 text-gray-700'
                          }`}>
                            {link.platform}
                          </span>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRemoveLink(link.id)}
                            className="text-gray-400 hover:text-red-500 p-1"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 批量处理配置 */}
              <div className="bg-white p-4 rounded-lg border">
                <div className="text-sm font-medium text-gray-700 mb-4">⚙️ 批量处理配置</div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* 模型选择 */}
                  <FormField
                    control={form.control}
                    name="model_name"
                    render={({ field }) => (
                      <FormItem>
                        <div className="text-sm font-medium text-gray-600">AI模型</div>
                        <Select
                          value={field.value}
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="选择AI模型" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {modelList.map(m => (
                              <SelectItem key={m.id} value={m.model_name}>
                                {m.model_name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* 笔记风格 */}
                  <FormField
                    control={form.control}
                    name="style"
                    render={({ field }) => (
                      <FormItem>
                        <div className="text-sm font-medium text-gray-600">笔记风格</div>
                        <Select
                          value={field.value}
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="选择笔记风格" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {noteStyles.map(({ label, value }) => (
                              <SelectItem key={value} value={value}>
                                {label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* 笔记格式选项 */}
                <div className="mt-4">
                  <FormField
                    control={form.control}
                    name="format"
                    render={({ field }) => (
                      <FormItem>
                        <div className="text-sm font-medium text-gray-600 mb-2">笔记格式</div>
                        <CheckboxGroup
                          value={field.value}
                          onChange={field.onChange}
                          disabledMap={{
                            link: false, // 批量模式下不禁用链接
                            screenshot: !videoUnderstandingEnabled,
                          }}
                        />
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* 备注 */}
                <div className="mt-4">
                  <FormField
                    control={form.control}
                    name="extras"
                    render={({ field }) => (
                      <FormItem>
                        <div className="text-sm font-medium text-gray-600 mb-2">额外说明</div>
                        <Textarea
                          placeholder="可在此添加额外的处理说明..."
                          {...field}
                          className="min-h-[80px]"
                        />
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </div>
          )}

          {/* 单个处理模式的视频链接输入 */}
          {!isBatchMode && (
            <>
              <SectionHeader title="视频链接" tip="支持 B 站、YouTube 等平台" />
          <div className="flex gap-2">
            {/* 平台选择 */}

            <FormField
              control={form.control}
              name="platform"
              render={({ field }) => (
                <FormItem>
                  <Select
                    disabled={!!editing}
                    value={field.value}
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger className="w-32">
                        <SelectValue />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {videoPlatforms?.map(p => (
                        <SelectItem key={p.value} value={p.value}>
                          <div className="flex items-center justify-center gap-2">
                            <div className="h-4 w-4">{p.logo()}</div>
                            <span>{p.label}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            {/* 链接输入 / 上传框 */}
            <FormField
              control={form.control}
              name="video_url"
              render={({ field }) => (
                <FormItem className="flex-1">
                  <div className="flex gap-2">
                    {platform === 'local' ? (
                      <>
                        <Input disabled={!!editing} placeholder="请输入本地视频路径" {...field} className="flex-1" />
                      </>
                    ) : (
                      <>
                        <Input disabled={!!editing} placeholder="请输入视频网站链接" {...field} className="flex-1" />
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                type="button"
                                variant="outline"
                                size="icon"
                                onClick={handlePasteFromClipboard}
                                disabled={!!editing}
                                className="shrink-0"
                              >
                                <Clipboard className="h-4 w-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>从剪贴板粘贴链接</TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </>
                    )}
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="video_url"
            render={({ field }) => (
              <FormItem className="flex-1">
                {platform === 'local' && (
                  <>
                    <div
                      className="hover:border-primary mt-2 flex h-40 cursor-pointer items-center justify-center rounded-md border-2 border-dashed border-gray-300 transition-colors"
                      onDragOver={e => {
                        e.preventDefault()
                        e.stopPropagation()
                      }}
                      onDrop={e => {
                        e.preventDefault()
                        const file = e.dataTransfer.files?.[0]
                        if (file) handleFileUpload(file, field.onChange)
                      }}
                      onClick={() => {
                        const input = document.createElement('input')
                        input.type = 'file'
                        input.accept = 'video/*'
                        input.onchange = e => {
                          const file = (e.target as HTMLInputElement).files?.[0]
                          if (file) handleFileUpload(file, field.onChange)
                        }
                        input.click()
                      }}
                    >
                      <p className="text-center text-sm text-gray-500">
                        拖拽文件到这里上传 <br />
                        <span className="text-xs text-gray-400">或点击选择文件</span>
                      </p>
                    </div>
                  </>
                )}
                <FormMessage />
              </FormItem>
            )}
          />

            </>
          )}

          {/* 通用配置部分 - 只在单个处理模式下显示 */}
          {!isBatchMode && (
            <>
              <div className="grid grid-cols-2 gap-2">
                {/* 模型选择 */}
                <FormField
                  className="w-full"
                  control={form.control}
                  name="model_name"
                  render={({ field }) => (
                    <FormItem>
                      <SectionHeader title="模型选择" tip="不同模型效果不同，建议自行测试" />
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger className="w-full min-w-0 truncate">
                            <SelectValue />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {modelList.map(m => (
                            <SelectItem key={m.id} value={m.model_name}>
                              {m.model_name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                {/* 笔记风格 */}
                <FormField
                  className="w-full"
                  control={form.control}
                  name="style"
                  render={({ field }) => (
                    <FormItem>
                      <SectionHeader title="笔记风格" tip="选择生成笔记的呈现风格" />
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger className="w-full min-w-0 truncate">
                            <SelectValue />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {noteStyles.map(({ label, value }) => (
                            <SelectItem key={value} value={value}>
                              {label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* 视频理解 */}
              <SectionHeader title="视频理解" tip="将视频截图发给多模态模型辅助分析" />
              <div className="flex flex-col gap-2">
                <FormField
                  control={form.control}
                  name="video_understanding"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex items-center gap-2">
                        <FormLabel>启用</FormLabel>
                        <Checkbox
                          checked={videoUnderstandingEnabled}
                          onCheckedChange={v => form.setValue('video_understanding', v)}
                        />
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  {/* 采样间隔 */}
                  <FormField
                    control={form.control}
                    name="video_interval"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>采样间隔（秒）</FormLabel>
                        <Input disabled={!videoUnderstandingEnabled} type="number" {...field} />
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  {/* 拼图大小 */}
                  <FormField
                    control={form.control}
                    name="grid_size"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>拼图尺寸（列 × 行）</FormLabel>
                        <div className="flex items-center space-x-2">
                          <Input
                            disabled={!videoUnderstandingEnabled}
                            type="number"
                            value={field.value?.[0] || 3}
                            onChange={e => field.onChange([+e.target.value, field.value?.[1] || 3])}
                            className="w-16"
                          />
                          <span>x</span>
                          <Input
                            disabled={!videoUnderstandingEnabled}
                            type="number"
                            value={field.value?.[1] || 3}
                            onChange={e => field.onChange([field.value?.[0] || 3, +e.target.value])}
                            className="w-16"
                          />
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <Alert
                  closable
                  type="error"
                  message={
                    <div>
                      <strong>提示：</strong>
                      <p>视频理解功能必须使用多模态模型。</p>
                    </div>
                  }
                  className="text-sm"
                />
              </div>

              {/* 笔记格式 */}
              <FormField
                control={form.control}
                name="format"
                render={({ field }) => (
                  <FormItem>
                    <SectionHeader title="笔记格式" tip="选择要包含的笔记元素" />
                    <CheckboxGroup
                      value={field.value}
                      onChange={field.onChange}
                      disabledMap={{
                        link: platform === 'local',
                        screenshot: !videoUnderstandingEnabled,
                      }}
                    />
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* 备注 */}
              <FormField
                control={form.control}
                name="extras"
                render={({ field }) => (
                  <FormItem>
                    <SectionHeader title="备注" tip="可在 Prompt 结尾附加自定义说明" />

                    {/* 快捷按钮区域 */}
                    <div className="mb-3">
                      <div className="mb-2 flex items-center justify-between">
                        <span className="text-xs text-gray-500">快捷提示词：</span>
                        {field.value && (
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => field.onChange('')}
                            className="h-6 px-2 text-xs text-gray-400 hover:text-red-500"
                          >
                            清空
                          </Button>
                        )}
                      </div>
                      <div className="flex flex-wrap gap-2">
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            const currentValue = field.value || ''
                            const newText = '请重点关注反常识的点'
                            const updatedValue = currentValue ? `${currentValue}\n${newText}` : newText
                            field.onChange(updatedValue)
                          }}
                          className="text-xs hover:bg-blue-50 hover:border-blue-300 transition-colors"
                        >
                          🤔 反常识的点
                        </Button>

                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            const currentValue = field.value || ''
                            const newText = '请重点关注跨界融合的点'
                            const updatedValue = currentValue ? `${currentValue}\n${newText}` : newText
                            field.onChange(updatedValue)
                          }}
                          className="text-xs hover:bg-green-50 hover:border-green-300 transition-colors"
                        >
                          🔗 跨界融合的点
                        </Button>

                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            const currentValue = field.value || ''
                            const newText = '请重点关注亮点'
                            const updatedValue = currentValue ? `${currentValue}\n${newText}` : newText
                            field.onChange(updatedValue)
                          }}
                          className="text-xs hover:bg-yellow-50 hover:border-yellow-300 transition-colors"
                        >
                          ✨ 亮点
                        </Button>

                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            const currentValue = field.value || ''
                            const newText = '请提炼核心观点和要点'
                            const updatedValue = currentValue ? `${currentValue}\n${newText}` : newText
                            field.onChange(updatedValue)
                          }}
                          className="text-xs hover:bg-purple-50 hover:border-purple-300 transition-colors"
                        >
                          🎯 核心观点
                        </Button>

                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            const currentValue = field.value || ''
                            const newText = '请关注实用技巧和方法论'
                            const updatedValue = currentValue ? `${currentValue}\n${newText}` : newText
                            field.onChange(updatedValue)
                          }}
                          className="text-xs hover:bg-orange-50 hover:border-orange-300 transition-colors"
                        >
                          🛠️ 实用技巧
                        </Button>
                      </div>
                    </div>

                    <Textarea placeholder="笔记需要罗列出 xxx 关键点…" {...field} />
                    <FormMessage />
                  </FormItem>
                )}
              />
            </>
          )}
        </form>
      </Form>
    </div>
  )
}

export default NoteForm
