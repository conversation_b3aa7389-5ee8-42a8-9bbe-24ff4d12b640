import { useEffect, useRef } from 'react'
import { useTaskStore } from '@/store/taskStore'
import { get_task_status } from '@/services/note.ts'
import toast from 'react-hot-toast'

export const useTaskPolling = (interval = 5000) => { // 增加轮询间隔到5秒
  const tasks = useTaskStore(state => state.tasks)
  const updateTaskContent = useTaskStore(state => state.updateTaskContent)
  const updateTaskStatus = useTaskStore(state => state.updateTaskStatus)
  const removeTask = useTaskStore(state => state.removeTask)

  const tasksRef = useRef(tasks)
  const pollingCountRef = useRef<Record<string, number>>({}) // 记录每个任务的轮询次数

  // 每次 tasks 更新，把最新的 tasks 同步进去
  useEffect(() => {
    tasksRef.current = tasks
  }, [tasks])

  useEffect(() => {
    const timer = setInterval(async () => {
      const pendingTasks = tasksRef.current.filter(
        task => task.status !== 'SUCCESS' && task.status !== 'FAILED'
      )

      // 如果没有待处理的任务，跳过本次轮询
      if (pendingTasks.length === 0) {
        console.log('📝 没有待处理的任务，跳过轮询')
        return
      }

      console.log(`🔄 开始轮询 ${pendingTasks.length} 个任务`)

      // 限制并发轮询数量，避免服务器压力过大
      const maxConcurrent = 3
      for (let i = 0; i < pendingTasks.length; i += maxConcurrent) {
        const batch = pendingTasks.slice(i, i + maxConcurrent)

        await Promise.allSettled(
          batch.map(async (task) => {
            try {
              // 初始化轮询计数
              if (!pollingCountRef.current[task.id]) {
                pollingCountRef.current[task.id] = 0
              }

              pollingCountRef.current[task.id]++

              // 设置最大轮询次数限制（5分钟 * 60秒 / 5秒 = 60次）
              const maxPollingCount = 60
              if (pollingCountRef.current[task.id] > maxPollingCount) {
                console.warn(`⚠️ 任务 ${task.id} 轮询超时，标记为失败`)
                updateTaskContent(task.id, { status: 'FAILED' })
                delete pollingCountRef.current[task.id]
                toast.error(`任务 ${task.id.slice(0, 8)} 处理超时`)
                return
              }

              console.log(`🔄 轮询任务 ${task.id.slice(0, 8)} (第${pollingCountRef.current[task.id]}次)`)
              const res = await get_task_status(task.id)
              const { status } = res.data

              if (status && status !== task.status) {
                if (status === 'SUCCESS') {
                  const { markdown, transcript, audio_meta } = res.data.result
                  toast.success(`笔记生成成功: ${task.id.slice(0, 8)}`)
                  updateTaskContent(task.id, {
                    status,
                    markdown,
                    transcript,
                    audioMeta: audio_meta,
                  })
                  // 清理轮询计数
                  delete pollingCountRef.current[task.id]
                } else if (status === 'FAILED') {
                  updateTaskContent(task.id, { status })
                  console.warn(`⚠️ 任务 ${task.id.slice(0, 8)} 失败`)
                  toast.error(`任务失败: ${task.id.slice(0, 8)}`)
                  // 清理轮询计数
                  delete pollingCountRef.current[task.id]
                } else {
                  // 更新中间状态
                  updateTaskContent(task.id, { status })
                  console.log(`📋 任务 ${task.id.slice(0, 8)} 状态: ${status}`)
                }
              }
            } catch (e) {
              console.error(`❌ 任务 ${task.id.slice(0, 8)} 轮询失败:`, e)

              // 网络错误时不立即标记为失败，给几次重试机会
              if (pollingCountRef.current[task.id] > 10) {
                toast.error(`任务 ${task.id.slice(0, 8)} 网络错误`)
                updateTaskContent(task.id, { status: 'FAILED' })
                delete pollingCountRef.current[task.id]
              }
            }
          })
        )

        // 批次间稍作延迟，避免服务器压力
        if (i + maxConcurrent < pendingTasks.length) {
          await new Promise(resolve => setTimeout(resolve, 500))
        }
      }
    }, interval)

    return () => {
      clearInterval(timer)
      // 清理轮询计数
      pollingCountRef.current = {}
    }
  }, [interval])
}
