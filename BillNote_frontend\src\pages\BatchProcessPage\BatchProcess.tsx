import { FC, useState, useRef } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Upload, FileText, Link, Play, Trash2, Download } from 'lucide-react'
import toast from 'react-hot-toast'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import { parseMarkdown, batchGenerateNotes } from '@/services/note'
import { useTaskStore } from '@/store/taskStore'
import { useModelStore } from '@/store/modelStore'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'

interface ExtractedLink {
  id: string
  url: string
  title?: string
  platform: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  taskId?: string
  error?: string
}

interface BatchProcessPageProps {}

const BatchProcessPage: FC<BatchProcessPageProps> = () => {
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { addPendingTask } = useTaskStore()
  const { modelList } = useModelStore()

  const [markdownContent, setMarkdownContent] = useState('')
  const [extractedLinks, setExtractedLinks] = useState<ExtractedLink[]>([])
  const [isProcessing, setBatchProcessing] = useState(false)
  const [progress, setProgress] = useState(0)

  // 批量处理配置
  const [batchConfig, setBatchConfig] = useState({
    quality: 'medium',
    model_name: modelList[0]?.model_name || '',
    provider_id: modelList[0]?.provider_id || '',
    style: 'minimal',
    format: [] as string[],
    screenshot: false,
    link: false,
    video_understanding: false,
    video_interval: 4,
    grid_size: [3, 3] as [number, number],
    extras: ''
  })

  // 支持的平台检测
  const detectPlatform = (url: string): string => {
    if (url.includes('bilibili.com')) return 'bilibili'
    if (url.includes('youtube.com') || url.includes('youtu.be')) return 'youtube'
    if (url.includes('douyin.com')) return 'douyin'
    if (url.includes('kuaishou.com')) return 'kuaishou'
    return 'unknown'
  }

  // 从后端API解析Markdown内容
  const extractLinksFromMarkdown = async (content: string): Promise<ExtractedLink[]> => {
    try {
      const result = await parseMarkdown(content)
      if (!result) return []

      return result.links.map((link: any) => ({
        id: `link-${Date.now()}-${Math.random()}`,
        url: link.url,
        title: link.title,
        platform: link.platform,
        status: 'pending' as const
      }))
    } catch (error) {
      console.error('解析链接失败:', error)
      return []
    }
  }

  // 处理文件上传
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    if (!file.name.endsWith('.md')) {
      toast.error('请上传 .md 格式的文件')
      return
    }

    const reader = new FileReader()
    reader.onload = async (e) => {
      const content = e.target?.result as string
      setMarkdownContent(content)
      const links = await extractLinksFromMarkdown(content)
      setExtractedLinks(links)

      toast.success(`文件解析成功，发现 ${links.length} 个有效视频链接`)
    }
    reader.readAsText(file)
  }

  // 手动解析Markdown内容
  const handleParseMarkdown = async () => {
    if (!markdownContent.trim()) {
      toast.error('请输入或上传Markdown内容')
      return
    }

    const links = await extractLinksFromMarkdown(markdownContent)
    setExtractedLinks(links)

    toast.success(`解析完成，发现 ${links.length} 个有效视频链接`)
  }

  // 删除单个链接
  const handleRemoveLink = (linkId: string) => {
    setExtractedLinks(prev => prev.filter(link => link.id !== linkId))
  }

  // 开始批量处理
  const handleBatchProcess = async () => {
    if (extractedLinks.length === 0) {
      toast.error('没有可处理的链接，请先解析Markdown内容')
      return
    }

    if (!batchConfig.model_name || !batchConfig.provider_id) {
      toast.error('请先配置AI模型')
      return
    }

    setBatchProcessing(true)
    setProgress(0)

    try {
      // 调用批量处理API
      const result = await batchGenerateNotes({
        links: extractedLinks.map(link => ({
          url: link.url,
          title: link.title,
          platform: link.platform
        })),
        quality: batchConfig.quality,
        model_name: batchConfig.model_name,
        provider_id: batchConfig.provider_id,
        format: batchConfig.format,
        style: batchConfig.style,
        extras: batchConfig.extras,
        screenshot: batchConfig.screenshot,
        link: batchConfig.link,
        video_understanding: batchConfig.video_understanding,
        video_interval: batchConfig.video_interval,
        grid_size: batchConfig.grid_size
      })

      if (result) {
        // 将任务添加到任务存储中
        result.tasks.forEach((task: any) => {
          addPendingTask(task.task_id, task.platform, {
            video_url: task.url,
            platform: task.platform,
            quality: batchConfig.quality,
            model_name: batchConfig.model_name,
            provider_id: batchConfig.provider_id
          })
        })

        // 更新链接状态
        setExtractedLinks(prev => prev.map(link => ({
          ...link,
          status: 'processing' as const
        })))

        setProgress(100)
        toast.success(`批量处理已提交，共 ${result.total_count} 个任务正在处理中`)
      }
    } catch (error) {
      console.error('批量处理失败:', error)
      toast.error('批量处理失败，请稍后重试')
    } finally {
      setBatchProcessing(false)
    }
  }

  // 获取平台颜色
  const getPlatformColor = (platform: string) => {
    switch (platform) {
      case 'bilibili': return 'bg-pink-100 text-pink-800'
      case 'youtube': return 'bg-red-100 text-red-800'
      case 'douyin': return 'bg-blue-100 text-blue-800'
      case 'kuaishou': return 'bg-orange-100 text-orange-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-gray-100 text-gray-800'
      case 'processing': return 'bg-blue-100 text-blue-800'
      case 'completed': return 'bg-green-100 text-green-800'
      case 'failed': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">批量处理</h1>
          <p className="text-muted-foreground">导入包含多个视频链接的Markdown文档，一键批量生成笔记</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 左侧：文件上传和内容输入 */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="h-5 w-5" />
                导入Markdown文档
              </CardTitle>
              <CardDescription>
                上传包含视频链接的.md文件，或直接粘贴Markdown内容
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="file-upload">选择文件</Label>
                <Input
                  id="file-upload"
                  type="file"
                  accept=".md"
                  ref={fileInputRef}
                  onChange={handleFileUpload}
                  className="mt-1"
                />
              </div>

              <Separator />

              <div>
                <Label htmlFor="markdown-content">或直接输入Markdown内容</Label>
                <Textarea
                  id="markdown-content"
                  placeholder="粘贴包含视频链接的Markdown内容..."
                  value={markdownContent}
                  onChange={(e) => setMarkdownContent(e.target.value)}
                  className="mt-1 min-h-[200px]"
                />
              </div>

              <Button onClick={handleParseMarkdown} className="w-full">
                <FileText className="h-4 w-4 mr-2" />
                解析链接
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* 中间：批量处理配置 */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>批量处理配置</CardTitle>
              <CardDescription>
                配置批量处理的参数，将应用于所有视频链接
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="model-select">AI模型</Label>
                <Select
                  value={batchConfig.model_name}
                  onValueChange={(value) => {
                    const model = modelList.find(m => m.model_name === value)
                    setBatchConfig(prev => ({
                      ...prev,
                      model_name: value,
                      provider_id: model?.provider_id || ''
                    }))
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择AI模型" />
                  </SelectTrigger>
                  <SelectContent>
                    {modelList.map((model) => (
                      <SelectItem key={model.model_name} value={model.model_name}>
                        {model.model_name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="quality-select">处理质量</Label>
                <Select
                  value={batchConfig.quality}
                  onValueChange={(value) => setBatchConfig(prev => ({ ...prev, quality: value }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="fast">快速</SelectItem>
                    <SelectItem value="medium">中等</SelectItem>
                    <SelectItem value="slow">高质量</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="style-select">笔记风格</Label>
                <Select
                  value={batchConfig.style}
                  onValueChange={(value) => setBatchConfig(prev => ({ ...prev, style: value }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="minimal">简洁风格</SelectItem>
                    <SelectItem value="detailed">详细风格</SelectItem>
                    <SelectItem value="academic">学术风格</SelectItem>
                    <SelectItem value="casual">口语风格</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>笔记格式</Label>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="screenshot"
                      checked={batchConfig.screenshot}
                      onCheckedChange={(checked) => setBatchConfig(prev => ({ ...prev, screenshot: !!checked }))}
                    />
                    <Label htmlFor="screenshot">包含截图</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="link"
                      checked={batchConfig.link}
                      onCheckedChange={(checked) => setBatchConfig(prev => ({ ...prev, link: !!checked }))}
                    />
                    <Label htmlFor="link">包含跳转链接</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="video_understanding"
                      checked={batchConfig.video_understanding}
                      onCheckedChange={(checked) => setBatchConfig(prev => ({ ...prev, video_understanding: !!checked }))}
                    />
                    <Label htmlFor="video_understanding">视频理解</Label>
                  </div>
                </div>
              </div>

              <div>
                <Label htmlFor="extras">额外说明</Label>
                <Textarea
                  id="extras"
                  placeholder="可在此添加额外的处理说明..."
                  value={batchConfig.extras}
                  onChange={(e) => setBatchConfig(prev => ({ ...prev, extras: e.target.value }))}
                  className="mt-1"
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 右侧：链接列表和处理控制 */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span className="flex items-center gap-2">
                  <Link className="h-5 w-5" />
                  发现的视频链接 ({extractedLinks.length})
                </span>
                {extractedLinks.length > 0 && (
                  <Button
                    onClick={handleBatchProcess}
                    disabled={isProcessing}
                    size="sm"
                  >
                    <Play className="h-4 w-4 mr-2" />
                    {isProcessing ? '处理中...' : '开始批量处理'}
                  </Button>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {isProcessing && (
                <div className="mb-4">
                  <div className="flex justify-between text-sm mb-2">
                    <span>处理进度</span>
                    <span>{Math.round(progress)}%</span>
                  </div>
                  <Progress value={progress} />
                </div>
              )}

              <ScrollArea className="h-[400px]">
                {extractedLinks.length === 0 ? (
                  <div className="text-center text-muted-foreground py-8">
                    <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>暂无发现的视频链接</p>
                    <p className="text-sm">请上传或输入包含视频链接的Markdown内容</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {extractedLinks.map((link) => (
                      <div key={link.id} className="border rounded-lg p-3 space-y-2">
                        <div className="flex items-start justify-between">
                          <div className="flex-1 min-w-0">
                            <p className="font-medium truncate">{link.title}</p>
                            <p className="text-sm text-muted-foreground truncate">{link.url}</p>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRemoveLink(link.id)}
                            className="ml-2"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                        <div className="flex gap-2">
                          <Badge className={getPlatformColor(link.platform)}>
                            {link.platform}
                          </Badge>
                          <Badge className={getStatusColor(link.status)}>
                            {link.status === 'pending' && '待处理'}
                            {link.status === 'processing' && '处理中'}
                            {link.status === 'completed' && '已完成'}
                            {link.status === 'failed' && '失败'}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </ScrollArea>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

export default BatchProcessPage
